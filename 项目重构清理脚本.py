#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
COT-DIR项目重构清理脚本
====================

这个脚本用于清理COT-DIR项目中的冗余文件，提高项目的可维护性。

功能：
1. 删除重复的演示程序
2. 删除旧版本的求解器
3. 删除冗余的LaTeX文件
4. 删除临时和生成的文件
5. 清理缓存目录
6. 保留核心功能文件

使用方法：
python 项目重构清理脚本.py --dry-run    # 预览模式
python 项目重构清理脚本.py              # 执行清理
"""

import os
import shutil
import argparse
from pathlib import Path
from typing import List, Dict
import json

class ProjectRefactor:
    def __init__(self, project_root: str, dry_run: bool = False):
        self.project_root = Path(project_root)
        self.dry_run = dry_run
        self.deleted_files = []
        self.deleted_dirs = []
        self.preserved_files = []
        
    def log_action(self, action: str, path: str, reason: str = ""):
        """记录操作日志"""
        if self.dry_run:
            print(f"[DRY-RUN] {action}: {path} {reason}")
        else:
            print(f"[EXECUTE] {action}: {path} {reason}")
    
    def safe_delete_file(self, file_path: Path, reason: str = ""):
        """安全删除文件"""
        if file_path.exists():
            self.log_action("DELETE FILE", str(file_path), f"- {reason}")
            if not self.dry_run:
                file_path.unlink()
                self.deleted_files.append(str(file_path))
        else:
            self.log_action("NOT FOUND", str(file_path), f"- {reason}")
    
    def safe_delete_dir(self, dir_path: Path, reason: str = ""):
        """安全删除目录"""
        if dir_path.exists() and dir_path.is_dir():
            self.log_action("DELETE DIR", str(dir_path), f"- {reason}")
            if not self.dry_run:
                shutil.rmtree(dir_path)
                self.deleted_dirs.append(str(dir_path))
        else:
            self.log_action("NOT FOUND", str(dir_path), f"- {reason}")
    
    def preserve_file(self, file_path: Path, reason: str = ""):
        """标记保留文件"""
        if file_path.exists():
            self.log_action("PRESERVE", str(file_path), f"- {reason}")
            self.preserved_files.append(str(file_path))
    
    def clean_demo_files(self):
        """清理演示程序冗余"""
        print("\n🎯 清理演示程序冗余...")
        
        # 保留的核心演示程序
        core_demos = [
            "single_question_demo.py",
            "complete_cotdir_demo.py", 
            "simplified_cases_demo.py"
        ]
        
        # 要删除的冗余演示程序
        redundant_demos = [
            "dir_focused_evaluation_demo.py",
            "standalone_optimization_demo.py",
            "demo_intelligent_tutor.py",
            "experiment_framework_demo.py",
            "experimental_validation_demo.py",
            "simple_experiment_demo.py",
            "detailed_solution_demo.py",
            "demo_enhanced_integration.py",
            "quick_enhanced_demo.py",
            "cases_results_demo.py",
            "cotdir_core_solution_demo.py",
            "comprehensive_optimization_demo.py",
            "demo_refactored_system.py"
        ]
        
        # 保留核心演示
        for demo in core_demos:
            self.preserve_file(self.project_root / demo, "核心演示程序")
        
        # 删除冗余演示
        for demo in redundant_demos:
            self.safe_delete_file(self.project_root / demo, "冗余演示程序")
    
    def clean_solver_files(self):
        """清理求解器文件冗余"""
        print("\n🔧 清理求解器文件冗余...")
        
        # 保留的核心求解器
        core_solvers = [
            "src/reasoning_engine/pattern_based_solver.py",
            "src/math_problem_solver_v2.py"
        ]
        
        # 要删除的冗余求解器
        redundant_solvers = [
            "src/math_problem_solver.py",
            "legacy/math_problem_solver.py",
            "legacy/math_problem_solver_optimized.py",
            "legacy/math_problem_solver_v2.py"
        ]
        
        # 保留核心求解器
        for solver in core_solvers:
            self.preserve_file(self.project_root / solver, "核心求解器")
        
        # 删除冗余求解器
        for solver in redundant_solvers:
            self.safe_delete_file(self.project_root / solver, "冗余求解器")
    
    def clean_latex_files(self):
        """清理LaTeX文件冗余"""
        print("\n📄 清理LaTeX文件冗余...")
        
        # 保留的核心LaTeX文件
        core_latex = [
            "FINAL_CORRECTED_EXPERIMENTAL_SECTION.tex",
            "performance_analysis_section.tex",
            "credible_sota_performance_table.tex",
            "ablation_study_table.tex"
        ]
        
        # 要删除的冗余LaTeX文件
        redundant_latex = [
            "ACADEMICALLY_RIGOROUS_EXPERIMENTAL_SECTION.tex",
            "COMPLETE_EXPERIMENT_SECTION.tex",
            "CORRECTED_EXPERIMENTAL_SECTION.tex",
            "CORRECTED_EXPERIMENTAL_SECTION_FINAL.tex",
            "IMPROVED_EXPERIMENTAL_SECTION.tex",
            "REVISED_EXPERIMENTAL_SECTION_FOCUSED.tex",
            "IMPROVED_CONCLUSION_SECTION.tex",
            "OPTIMIZED_CONCLUSION_SECTION.tex",
            "temp_exp.tex"
        ]
        
        # 保留核心LaTeX文件
        for latex in core_latex:
            self.preserve_file(self.project_root / latex, "核心LaTeX文件")
        
        # 删除冗余LaTeX文件
        for latex in redundant_latex:
            self.safe_delete_file(self.project_root / latex, "冗余LaTeX文件")
    
    def clean_verification_scripts(self):
        """清理验证和改进脚本冗余"""
        print("\n🔍 清理验证脚本冗余...")
        
        # 保留的核心脚本
        core_scripts = [
            "experimental_framework.py",
            "batch_complexity_classifier.py"
        ]
        
        # 要删除的冗余脚本
        redundant_scripts = [
            "verify_complete_experiment_consistency.py",
            "verify_paper_data_consistency.py",
            "validate_improved_data.py",
            "improve_experimental_data.py",
            "conservative_data_improvement.py",
            "generate_paper_experimental_data.py",
            "cotdir_verification_tool.py",
            "test_optimization.py",
            "fixed_count_problems.py",
            "count_problems.py"
        ]
        
        # 保留核心脚本
        for script in core_scripts:
            self.preserve_file(self.project_root / script, "核心验证脚本")
        
        # 删除冗余脚本
        for script in redundant_scripts:
            self.safe_delete_file(self.project_root / script, "冗余验证脚本")
    
    def clean_generator_files(self):
        """清理结果生成器冗余"""
        print("\n📊 清理结果生成器冗余...")
        
        # 保留的核心生成器
        core_generators = [
            "detailed_case_results_generator.py",
            "enhanced_case_results_generator.py"
        ]
        
        # 要删除的冗余生成器
        redundant_generators = [
            "comprehensive_solution_generator.py",
            "enhanced_solution_generator.py",
            "final_cotdir_generator.py",
            "full_relation_generator.py",
            "maximum_solution_generator.py",
            "relation_based_solution_generator.py",
            "generate_final_solutions.py",
            "meta_pattern_generator.py",
            "pattern_library_enhancer.py"
        ]
        
        # 保留核心生成器
        for generator in core_generators:
            self.preserve_file(self.project_root / generator, "核心结果生成器")
        
        # 删除冗余生成器
        for generator in redundant_generators:
            self.safe_delete_file(self.project_root / generator, "冗余结果生成器")
    
    def clean_temp_files(self):
        """清理临时和生成文件"""
        print("\n🗑️ 清理临时文件...")
        
        # JSON结果文件
        json_files = [
            "detailed_case_results.json",
            "simplified_case_results.json",
            "enhanced_solutions_20250630_013304.json",
            "maximum_solutions_20250630_013528.json",
            "relation_based_solutions_20250630_015026.json",
            "solutions_20250630_013109.json",
            "solutions_20250630_013117.json",
            "full_relation_solutions.json",
            "experimental_capability_report_20250628_184617.json",
            "experimental_capability_report_20250628_192313.json",
            "paper_experimental_data_20250628_194756.json",
            "complete_experiment_consistency_report.json",
            "paper_data_consistency_report.json",
            "merge_analysis.json",
            "failure_analysis.json",
            "failure_analysis_equation_baseline.json",
            "failure_analysis_report.json",
            "failure_analysis_template_baseline.json"
        ]
        
        # 生成的模式文件
        pattern_files = [
            "enhanced_patterns.json",
            "meta_patterns.json", 
            "generated_concrete_patterns.json"
        ]
        
        # 删除临时JSON文件
        for json_file in json_files + pattern_files:
            self.safe_delete_file(self.project_root / json_file, "临时生成文件")
    
    def clean_directories(self):
        """清理冗余目录"""
        print("\n📁 清理冗余目录...")
        
        # 要删除的目录
        redundant_dirs = [
            "legacy",
            "__pycache__",
            "enhanced_results",
            "classification_results"
        ]
        
        for dir_name in redundant_dirs:
            self.safe_delete_dir(self.project_root / dir_name, "冗余目录")
        
        # 清理所有__pycache__目录
        for pycache in self.project_root.rglob("__pycache__"):
            self.safe_delete_dir(pycache, "Python缓存目录")
    
    def generate_report(self):
        """生成清理报告"""
        report = {
            "summary": {
                "deleted_files_count": len(self.deleted_files),
                "deleted_dirs_count": len(self.deleted_dirs),
                "preserved_files_count": len(self.preserved_files)
            },
            "deleted_files": self.deleted_files,
            "deleted_dirs": self.deleted_dirs,
            "preserved_files": self.preserved_files
        }
        
        report_file = self.project_root / "重构清理报告.json"
        if not self.dry_run:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"\n📋 清理报告已保存到: {report_file}")
        
        return report
    
    def run_refactor(self):
        """执行完整的重构清理"""
        print("🚀 开始COT-DIR项目重构清理...")
        if self.dry_run:
            print("⚠️  预览模式 - 不会实际删除文件")
        
        # 执行各种清理操作
        self.clean_demo_files()
        self.clean_solver_files()
        self.clean_latex_files()
        self.clean_verification_scripts()
        self.clean_generator_files()
        self.clean_temp_files()
        self.clean_directories()
        
        # 生成报告
        report = self.generate_report()
        
        print(f"\n✅ 重构清理完成!")
        print(f"📊 删除文件: {report['summary']['deleted_files_count']}个")
        print(f"📊 删除目录: {report['summary']['deleted_dirs_count']}个")
        print(f"📊 保留文件: {report['summary']['preserved_files_count']}个")
        
        if self.dry_run:
            print("\n💡 要执行实际清理，请运行: python 项目重构清理脚本.py")

def main():
    parser = argparse.ArgumentParser(description="COT-DIR项目重构清理脚本")
    parser.add_argument("--dry-run", action="store_true", help="预览模式，不实际删除文件")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    
    args = parser.parse_args()
    
    refactor = ProjectRefactor(args.project_root, args.dry_run)
    refactor.run_refactor()

if __name__ == "__main__":
    main()
